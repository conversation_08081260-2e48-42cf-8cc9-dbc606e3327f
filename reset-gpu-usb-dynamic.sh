#!/bin/bash
# Reset AMD USB controller (1002:73a6) dynamically

# Use lspci to find the PCI address based on Vendor/Device ID and USB controller class
PCI_ADDR=$(lspci -Dvmmnn -d 1002:73a6 | awk '
    BEGIN {pci_addr=""}
    /^Slot:/ {pci_addr=$2}
    /^Class:/ {
        if ($0 ~ /USB controller.*0c03/) {
            class_match=1
        } else {
            class_match=0
        }
    }
    /^Vendor:/ {
        if ($0 ~ /\[1002\]/) {
            vendor_match=1
        } else {
            vendor_match=0
        }
    }
    /^Device:/ {
        if ($0 ~ /\[73a6\]/) {
            device_match=1
        } else {
            device_match=0
        }
    }
    END {
        if (class_match && vendor_match && device_match && pci_addr != "") {
            print pci_addr
        }
    }
')

if [[ -z "$PCI_ADDR" ]]; then
  echo "Error: AMD USB controller (1002:73a6) not found!"
  exit 1
fi

# Verify driver directory
DRIVER_DIR="/sys/bus/pci/drivers/xhci_hcd"
if [[ ! -d "$DRIVER_DIR" ]]; then
  echo "Error: xHCI driver directory not found. Is xhci_hcd loaded?"
  exit 1
fi

# Reset the device
echo "Resetting $PCI_ADDR..."
echo "$PCI_ADDR" | sudo tee "$DRIVER_DIR/unbind" > /dev/null
sleep 2
echo "$PCI_ADDR" | sudo tee "$DRIVER_DIR/bind" > /dev/null

echo "Successfully reset AMD USB controller at $PCI_ADDR."
