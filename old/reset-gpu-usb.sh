#!/bin/bash
# Reset the AMD USB controller connected to the GPU

# Extract PCI address dynamically using Vendor/Device ID
PCI_ADDR=$(lspci -Dvmmnn | awk -F '\t' '
    BEGIN {PCI_ADDR=""}
    /^Slot:/ { slot = $2 }
    /^Vendor:\t1002/ && /^Device:\t73a6/ && /^Class:\t0c03/ { PCI_ADDR = slot }
    END { print PCI_ADDR }
')

if [[ -z "$PCI_ADDR" ]]; then
  echo "Error: AMD USB controller (1002:73a6) not found!"
  exit 1
fi

# Check if the xHCI driver directory exists
DRIVER_DIR="/sys/bus/pci/drivers/xhci_hcd"
if [[ ! -d "$DRIVER_DIR" ]]; then
  echo "Error: xHCI driver directory not found. Is xhci_hcd loaded?"
  exit 1
fi

# Reset the device
echo "Resetting $PCI_ADDR..."
echo "$PCI_ADDR" | sudo tee "$DRIVER_DIR/unbind" > /dev/null
sleep 2
echo "$PCI_ADDR" | sudo tee "$DRIVER_DIR/bind" > /dev/null

echo "Successfully reset AMD USB controller at $PCI_ADDR."
